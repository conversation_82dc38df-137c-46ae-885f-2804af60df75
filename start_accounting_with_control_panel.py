#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل برنامج المحاسبة مع لوحة التحكم المركزية الجذابة
"""

import customtkinter as ctk
import sys
import os
from pathlib import Path
import tkinter.messagebox as messagebox

# إعداد المسارات
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def show_welcome_message():
    """عرض رسالة ترحيب"""
    welcome_text = """
🎛️ مرحباً بك في برنامج المحاسبة المطور

✨ الميزات الجديدة:
• لوحة تحكم مركزية جذابة
• 11 قسم شامل للإعدادات
• ألوان دافئة وحديثة (بدون رمادي)
• دعم كامل للعربية RTL
• نظام أمان متقدم
• نسخ احتياطي تلقائي

🚀 للوصول للوحة التحكم:
اضغط على أيقونة "الإعدادات" في الصف الأول

🎨 تم تصميم اللوحة بألوان دافئة جذابة:
مرجاني، برتقالي، ذهبي، نعناعي، لافندر، سماوي، وردي، خوخي

هل تريد المتابعة؟
    """
    
    result = messagebox.askyesno(
        "🎛️ برنامج المحاسبة المطور", 
        welcome_text.strip(),
        icon='question'
    )
    
    return result

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_modules = [
        'customtkinter',
        'tkinter',
        'sqlite3',
        'json',
        'pathlib'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_modules)}")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    return True

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات...")
    
    required_files = [
        'ui/main_window.py',
        'ui/central_control_panel.py',
        'themes/modern_theme.py',
        'config/settings.py',
        'config/control_panel_config.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ الملفات المفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات موجودة!")
    return True

def start_application():
    """تشغيل التطبيق"""
    print("\n🚀 بدء تشغيل البرنامج...")
    
    try:
        # إعداد customtkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # استيراد النافذة الرئيسية
        from ui.main_window import MainWindow
        
        print("✅ تم تحميل النافذة الرئيسية")
        
        # إنشاء التطبيق
        app = MainWindow()
        
        print("✅ تم إنشاء التطبيق بنجاح")
        print("\n🎛️ لوحة التحكم المركزية متاحة!")
        print("📍 اضغط على أيقونة 'الإعدادات' في الصف الأول")
        print("\n🎨 الألوان المستخدمة:")
        print("   • مرجاني دافئ، برتقالي غروب، ذهبي مشرق")
        print("   • نعناعي منعش، لافندر هادئ، سماوي صافي")
        print("   • وردي ناعم، خوخي فاتح، تركوازي")
        print("   • بنفسجي فاتح، وألوان دافئة أخرى")
        print("\n🚫 تم تجنب اللون الرمادي كلياً")
        
        # تشغيل التطبيق
        app.run()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        messagebox.showerror("خطأ", f"خطأ في تحميل البرنامج:\n{e}")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🎛️ برنامج المحاسبة مع لوحة التحكم المركزية الجذابة")
    print("=" * 70)
    
    # عرض رسالة الترحيب
    if not show_welcome_message():
        print("❌ تم إلغاء التشغيل من قبل المستخدم")
        return
    
    # فحص المتطلبات
    if not check_requirements():
        messagebox.showerror("خطأ", "بعض المتطلبات مفقودة!")
        return
    
    # فحص الملفات
    if not check_files():
        messagebox.showerror("خطأ", "بعض الملفات المطلوبة مفقودة!")
        return
    
    print("\n✅ جميع الفحوصات نجحت!")
    
    # تشغيل التطبيق
    start_application()

if __name__ == "__main__":
    main()
