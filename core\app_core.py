# -*- coding: utf-8 -*-
"""
النواة الأساسية للتطبيق
"""

import logging
import json
import os
from datetime import datetime
from config.settings import *

class AppCore:
    """النواة الأساسية للتطبيق"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.settings = self.load_settings()
        self.session_data = {}

    def load_settings(self):
        """تحميل إعدادات التطبيق"""
        try:
            settings_file = PROJECT_ROOT / "config" / "app_settings.json"

            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # إنشاء إعدادات افتراضية
                default_settings = {
                    "theme": "light",
                    "language": "ar",
                    "font_size": 12,
                    "auto_backup": True,
                    "backup_interval": 24,  # ساعات
                    "currency": "ريال سعودي",
                    "currency_symbol": "ر.س",
                    "tax_rate": 15.0,
                    "company_info": {
                        "name": "شركة ست الكل للمحاسبة",
                        "address": "",
                        "phone": "",
                        "email": "",
                        "tax_number": ""
                    }
                }

                self.save_settings(default_settings)
                return default_settings

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {e}")
            return {}

    def save_settings(self, settings=None):
        """حفظ إعدادات التطبيق"""
        try:
            if settings is None:
                settings = self.settings

            settings_file = PROJECT_ROOT / "config" / "app_settings.json"
            settings_file.parent.mkdir(parents=True, exist_ok=True)

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            self.settings = settings
            return True

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {e}")
            return False

    def get_setting(self, key, default=None):
        """الحصول على إعداد معين"""
        keys = key.split('.')
        value = self.settings

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def set_setting(self, key, value):
        """تعيين إعداد معين"""
        keys = key.split('.')
        settings = self.settings

        for k in keys[:-1]:
            if k not in settings:
                settings[k] = {}
            settings = settings[k]

        settings[keys[-1]] = value
        return self.save_settings()

    def format_currency(self, amount):
        """تنسيق المبلغ بالعملة"""
        try:
            symbol = self.get_setting('currency_symbol', 'ر.س')
            return f"{amount:,.2f} {symbol}"
        except:
            return f"{amount} ر.س"

    def calculate_tax(self, amount):
        """حساب الضريبة"""
        try:
            tax_rate = self.get_setting('tax_rate', 15.0)
            return amount * (tax_rate / 100)
        except:
            return amount * 0.15

    def log_activity(self, user_id, action, details=None):
        """تسجيل نشاط المستخدم"""
        try:
            activity = {
                'user_id': user_id,
                'action': action,
                'details': details,
                'timestamp': datetime.now().isoformat()
            }

            # حفظ في ملف السجل
            log_file = LOGS_PATH / f"activity_{datetime.now().strftime('%Y%m')}.json"

            activities = []
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    activities = json.load(f)

            activities.append(activity)

            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(activities, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل النشاط: {e}")
            return False

    def backup_database(self):
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            import shutil

            # مسار قاعدة البيانات الأصلية
            source_db = DATABASE_PATH

            # مسار النسخة الاحتياطية
            backup_dir = PROJECT_ROOT / "backups"
            backup_dir.mkdir(exist_ok=True)

            backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            backup_path = backup_dir / backup_filename

            # نسخ قاعدة البيانات
            shutil.copy2(source_db, backup_path)

            # الاحتفاظ بآخر 10 نسخ احتياطية فقط
            backup_files = sorted(backup_dir.glob("backup_*.db"))
            if len(backup_files) > 10:
                for old_backup in backup_files[:-10]:
                    old_backup.unlink()

            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_filename}")
            return True, str(backup_path)

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في النسخ الاحتياطي: {e}")
            return False, str(e)

    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:

            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"

            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            current_backup = DATABASE_PATH.parent / f"current_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            shutil.copy2(DATABASE_PATH, current_backup)

            # استعادة النسخة الاحتياطية
            shutil.copy2(backup_file, DATABASE_PATH)

            self.logger.info(f"تم استعادة قاعدة البيانات من: {backup_path}")
            return True, "تم استعادة قاعدة البيانات بنجاح"

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في استعادة قاعدة البيانات: {e}")
            return False, str(e)

    def get_system_info(self):
        """الحصول على معلومات النظام"""
        try:
            import platform
            import psutil

            return {
                'os': platform.system(),
                'os_version': platform.version(),
                'python_version': platform.python_version(),
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'memory_available': psutil.virtual_memory().available,
                'disk_usage': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
            }
        except:
            return {}

    def validate_license(self):
        """التحقق من صحة الترخيص"""
        # هذه وظيفة مؤقتة - يمكن تطويرها لاحقاً
        return True, "الترخيص صالح"

    def get_app_version(self):
        """الحصول على إصدار التطبيق"""
        return "1.0.0"

    def check_updates(self):
        """التحقق من التحديثات"""
        # وظيفة مؤقتة - يمكن تطويرها لاحقاً
        return False, "لا توجد تحديثات متاحة"

    def export_data(self, data_type, format_type='JSON'):
        """تصدير البيانات"""
        try:
            from database.database_manager import DatabaseManager

            db = DatabaseManager()
            export_dir = PROJECT_ROOT / "exports"
            export_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            if data_type == 'customers':
                data = db.fetch_all("SELECT * FROM customers WHERE is_active = 1")
                filename = f"customers_export_{timestamp}"
            elif data_type == 'products':
                data = db.fetch_all("SELECT * FROM products WHERE is_active = 1")
                filename = f"products_export_{timestamp}"
            elif data_type == 'sales':
                data = db.fetch_all("SELECT * FROM sales_invoices")
                filename = f"sales_export_{timestamp}"
            else:
                return False, "نوع البيانات غير مدعوم"

            if format_type.upper() == 'JSON':
                filepath = export_dir / f"{filename}.json"
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump([dict(row) for row in data], f, ensure_ascii=False, indent=2, default=str)

            elif format_type.upper() == 'CSV':
                import pandas as pd
                filepath = export_dir / f"{filename}.csv"
                df = pd.DataFrame([dict(row) for row in data])
                df.to_csv(filepath, index=False, encoding='utf-8-sig')

            else:
                return False, "نوع التصدير غير مدعوم"

            return True, str(filepath)

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في تصدير البيانات: {e}")
            return False, str(e)

    def import_data(self, filepath, data_type):
        """استيراد البيانات"""
        try:
            from database.database_manager import DatabaseManager

            db = DatabaseManager()
            file_path = Path(filepath)

            if not file_path.exists():
                return False, "الملف غير موجود"

            # قراءة البيانات
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            elif file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                data = df.to_dict('records')
            else:
                return False, "نوع الملف غير مدعوم"

            # استيراد البيانات حسب النوع
            success_count = 0
            error_count = 0

            for item in data:
                try:
                    if data_type == 'customers':
                        # استيراد العملاء
                        db.execute_query('''
                            INSERT OR REPLACE INTO customers 
                            (name, phone, email, address, tax_number, credit_limit, customer_type)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            item.get('name', ''),
                            item.get('phone', ''),
                            item.get('email', ''),
                            item.get('address', ''),
                            item.get('tax_number', ''),
                            item.get('credit_limit', 0),
                            item.get('customer_type', 'regular')
                        ))

                    elif data_type == 'products':
                        # استيراد المنتجات
                        db.execute_query('''
                            INSERT OR REPLACE INTO products 
                            (name, barcode, category, unit, cost_price, selling_price, min_stock, current_stock)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            item.get('name', ''),
                            item.get('barcode', ''),
                            item.get('category', ''),
                            item.get('unit', 'قطعة'),
                            item.get('cost_price', 0),
                            item.get('selling_price', 0),
                            item.get('min_stock', 0),
                            item.get('current_stock', 0)
                        ))

                    success_count += 1

                except Exception as e:
                    pass
                except Exception as e:
                    error_count += 1
                    self.logger.error(f"خطأ في استيراد العنصر: {e}")

            message = f"تم استيراد {success_count} عنصر بنجاح"
            if error_count > 0:
                message += f"، فشل في استيراد {error_count} عنصر"

            return True, message

        except Exception as e:
            self.logger.error(f"خطأ في استيراد البيانات: {e}")
            return False, str(e)
